

# Excella Development Rules and Standards (2025 Update)

## 0. Research and Development Guidelines

### 0.1 Pre-Development Research
- Always perform thorough research before implementing any feature or making changes
- Use Context7 and Tavily to check latest practices and documentation for:
  - Office Add-in development ([Office Add-ins Dev Kit for VS Code](https://marketplace.visualstudio.com/items?itemName=msoffice.microsoft-office-add-in-debugger))
  - Excel JavaScript API (latest: ExcelApi 1.17+)
  - React patterns and best practices (React 19 features: `useOptimistic`, `useActionState`, Suspense, etc.)
  - TypeScript configurations (TypeScript 5.x+, `verbatimModuleSyntax`)
  - Package versions and compatibility
- Validate proposed solutions against Microsoft's official documentation
- Check for breaking changes and deprecations in used APIs
- Reference the Security Guidelines section for dependency scanning and security checks
- Reference the Testing Requirements section for test runner standards
- Reference the Type Safety, React Components, and Excel Add-in Standards sections for implementation details

### 0.2 Code Generation Standards
- Always use Context7 to verify:
  - Latest API versions and features
  - Current best practices
  - Modern patterns and implementations (React 19, TypeScript 5.x)
  - Security considerations
  - Performance optimizations
- Reference the Documentation Standards section for TSDoc/JSDoc requirements
- Reference the Type Safety section for type definitions
- Reference the Error Handling guidelines in Excel Add-in and React Components sections

## 1. Project Structure & Naming Conventions

### 1.1 Directory and File Naming

#### File and Folder Names
- Use kebab-case for all files and folders in the directory structure
- Examples:
  - Folders: `data-services/`, `excel-functions/`, `ui-components/`
  - Configuration files: `tsconfig.json`, `webpack.config.js`, `jest.config.js`
  - Source files: `excel-handler.ts`, `data-transformer.ts`, `api-client.ts`
  - Test files: `excel-handler.test.ts`, `data-transformer.test.ts`
  - Style files: `component-name.styles.ts`
  - For Next.js apps, use file-based routing conventions

#### Component and Code Naming
- React Components: PascalCase (e.g., `DataAnalyzer.tsx`)
- Utilities: camelCase (e.g., `excelUtils.ts`)
- Custom Functions: camelCase (e.g., `dataTransform.ts`)
- Test Files: `{kebab-case-name}.test.ts`
- Style Files: `{kebab-case-name}.styles.ts`

### 1.3 Code Naming Conventions
- Custom Excel Functions: Prefix with `ex` (e.g., `exAnalyzeData`)
- React Components: Use descriptive PascalCase (e.g., `DataVisualizerPanel`)
- Event Handlers: Use `handle` prefix (e.g., `handleRangeSelection`)
- Async Functions: Use `async` prefix (e.g., `asyncLoadData`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_ROWS`)


## 2. Excel Add-in Specific Standards

### 2.1 Excel.run Pattern
// Use the latest ExcelApi version supported (e.g., 1.17+)
```typescript
await Excel.run(async (context) => {
    // Get references first
    const sheet = context.workbook.worksheets.getActiveWorksheet();
    const range = sheet.getRange("A1:D1");

    // Queue commands
    range.values = [["Header 1", "Header 2", "Header 3", "Header 4"]];
    range.format.fill.color = "#4472C4";

    // Perform sync at the end
    await context.sync();
});
```

### 2.2 Performance Best Practices
- Batch operations within single Excel.run
- Minimize context.sync() calls
- Cache worksheet references outside loops:
```typescript
// GOOD
const sheet = context.workbook.worksheets.getActiveWorksheet();
for (let i = 0; i < rows.length; i++) {
    sheet.getRange(`A${i}`).values = [[rows[i]]];
}

// BAD
for (let i = 0; i < rows.length; i++) {
    context.workbook.worksheets.getActiveWorksheet().getRange(`A${i}`).values = [[rows[i]]];
}
```


### 2.3 Error Handling
// Use type narrowing for error variables (TypeScript 4.4+). See also Error Handling in React Components Standards.
```typescript
try {
    await Excel.run(async (context) => {
        // Excel operations
    });
} catch (error) {
    if (error instanceof OfficeExtension.Error) {
        console.error(error.debugInfo);
        // Handle Office-specific errors
    } else {
        console.error(error);
        // Handle general errors
    }
}
```

### 2.4 Version Checking
// Always check for the latest requirement sets (see [Office Add-ins requirement sets](https://learn.microsoft.com/en-us/javascript/api/requirement-sets/))
```typescript
if (Office.context.requirements.isSetSupported("ExcelApi", "1.7")) {
    // Use newer APIs
} else {
    // Fallback implementation
}
```


## 3. React Components Standards

### 3.1 Component Structure
// Use React 19 features where possible (e.g., `useOptimistic`, `useActionState`, Suspense boundaries)
```typescript
import { useState, useEffect, useCallback, type FC } from 'react';
import { useExcelContext } from '@/contexts/excel-context';
import type { ComponentProps } from './types';

export const DataAnalyzer: FC<ComponentProps> = ({
    range,
    options,
    onAnalysisComplete
}) => {
    // State with proper typing
    const [data, setData] = useState<AnalysisData[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    // Context usage
    const { workbook } = useExcelContext();

    // Memoized callbacks
    const handleAnalyze = useCallback(async () => {
        if (!range) return;
        
        setIsLoading(true);
        setError(null);
        
        try {
            // Implementation with error handling
            const result = await analyzeData(range, options);
            setData(result);
            onAnalysisComplete?.(result);
        } catch (err) {
            setError(err instanceof Error ? err : new Error('Analysis failed'));
        } finally {
            setIsLoading(false);
        }
    }, [range, options, onAnalysisComplete]);

    // Effects with cleanup
    useEffect(() => {
        const controller = new AbortController();
        
        // Setup code
        
        return () => {
            controller.abort();
            // Additional cleanup
        };
    }, []);

    // Error boundary integration
    if (error) {
        return <ErrorDisplay error={error} onRetry={handleAnalyze} />;
    }

    // Loading state
    if (isLoading) {
        return <LoadingSpinner />;
    }

    // Main render
    return (
        // JSX with proper accessibility and error states
    );
};

// Preserve referential equality of default props
DataAnalyzer.defaultProps = {
    options: DEFAULT_ANALYSIS_OPTIONS
} as const;

// See also Error Handling in Excel Add-in Standards.
```

### 3.2 Custom Hooks
// Prefer function components and hooks; avoid legacy class components
```typescript
function useExcelRange(range: string) {
    const [values, setValues] = React.useState<any[][]>([]);

    React.useEffect(() => {
        // Excel.run implementation
    }, [range]);

    return values;
}
```

## 4. Testing Requirements

// Use modern test runners (Vitest, Jest 29+, Playwright for E2E). See also Pre-Development Research for research requirements.

### 4.1 Unit Tests
```typescript
describe('Excel Function Tests', () => {
    let context: Excel.RequestContext;

    beforeEach(() => {
        // Setup mock context
    });

    it('should process range data correctly', async () => {
        // Test implementation
    });
});
```

### 4.2 Integration Tests
- Test Excel interaction flows
- Verify data processing pipelines
- Test error handling scenarios

### 4.3 Test Coverage Requirements
- Minimum 80% code coverage
- All Excel interactions must be tested
- All error paths must be tested

## 5. Documentation Standards
// Use TSDoc/JSDoc for all exported functions, types, and components

### 5.1 Function Documentation
```typescript
/**
 * Analyzes the given range and returns insights
 * @customfunction
 * @param range The range to analyze
 * @param {string} options Analysis options
 * @returns {Promise<AnalysisResult>} The analysis result
 */
async function exAnalyzeRange(
    range: Excel.Range, 
    options: string
): Promise<AnalysisResult> {
    // Implementation
}
```

### 5.2 Component Documentation
```typescript
/**
 * DataVisualizer Component
 * Displays Excel data in various chart formats
 * 
 * @component
 * @example
 * ```tsx
 * <DataVisualizer range="A1:D10" type="bar" />
 * ```
 */
```


## 6. Performance Standards
// Use Fluent UI 2 for Office Add-in UI (2025+)

### 6.1 Excel and UI Performance
- Maximum batch size: 5000 cells (optimized for modern browsers)
- Minimum time between context.sync(): 250ms
- Use Range.values for bulk updates
- Implement virtual scrolling for large datasets
- Use worksheet level operations when possible
- Implement progressive loading for large data sets
- First Contentful Paint (FCP) < 1.5s
- Largest Contentful Paint (LCP) < 2.5s
- First Input Delay (FID) < 100ms
- Time to Interactive (TTI) < 2s
- Implement Suspense boundaries
- Use React.memo with useMemo/useCallback effectively
- Implement code splitting and lazy loading
- Use modern image formats (WebP, AVIF)
- Implement service workers for caching

## 7. Type Safety
// Use TypeScript 5.x+ features and strictest compiler options

### 7.1 TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": true,
    "target": "ES2022",
    "lib": ["DOM", "ES2022", "DOM.Iterable"],
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "incremental": true,
    "noUncheckedIndexedAccess": true,
    "verbatimModuleSyntax": true
  }
}
```

### 7.2 Type Definitions
```typescript
interface ExcelOperationResult {
    success: boolean;
    data?: any[];
    error?: {
        code: string;
        message: string;
    };
}
```

## 8. Security Guidelines

// Regularly scan dependencies (npm audit, Dependabot). See also Pre-Development Research for research requirements.


### 8.1 Data Handling
- Validate all input ranges
- Sanitize data before display
- Use proper error boundaries (see Error Handling in Excel Add-in and React Components Standards)
- Implement rate limiting for operations

### 8.2 Authentication
- Implement proper token management
- Use Microsoft Identity Platform for SSO in Office Add-ins
- Follow least privilege principle

## 9. Accessibility Standards
// Ensure all UI meets WCAG 2.1 AA and is tested with screen readers and keyboard navigation
