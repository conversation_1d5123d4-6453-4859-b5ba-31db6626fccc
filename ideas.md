1. when we are starting, we dont propose tech stack, rather functionalities that would be required. Then we will now run comprehensive rewsearch and do critical assessment of current available tools, software and frameworks that can help us perform those functionalities. 
3. Be gracious with the AI. Even you your context and memory has issues so lets do it one by one. Lets take one document build it, then AI references previous document for info to produce next etc.
4. UI/UX inspo fro Roo-Code, Augment Code, Cursor and Void and then Mobbin and Julius AI, Supabase and Postman.
5. With respect to data analysis, research and data science. what can AI agents do? Or how can AI or AI agents assist people who work in the financial, insurance, marketing etc fields who on a day-day use excel one way or another.
