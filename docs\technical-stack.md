# Technical Stack Documentation

## System Architecture Overview

### Frontend Components

#### Excel Add-in Technology Stack
- **Framework**: React 19.0.0 with TypeScript 5.6.0
- **UI Library**: Fluent UI React v9 (@fluentui/react-components@9.64.0)
- **Design System**: Fluent 2 Design System
- **Office Integration**: Office.js (ExcelApi 1.17+ with 1.4+ fallback)
- **State Management**: Zustand 4.4.7
- **Build Tools**: Webpack 5.89.0 with ts-loader 9.5.0
- **Development**: webpack-dev-server 4.15.0 with HTTPS support

#### Web Application Technology Stack
- **Framework**: Next.js 14.x (App Router, Edge Runtime)
- **Styling**: Tailwind CSS 3.x + shadcn/ui (latest)
- **State Management**: Zustand 4.4.7
- **Deployment**: Vercel Edge Runtime with regional optimization
- **Build Optimization**: Next.js compiler with SWC

### Backend Infrastructure

#### Core Backend (Supabase Stack)
- **Database**: PostgreSQL 15.x with Supabase extensions
- **Authentication**: Supabase Auth 2.x with OAuth providers
- **Real-time**: Supabase Realtime with WebSocket support
- **Storage**: Supabase Storage with CDN integration
- **Functions**: Supabase Edge Functions (Deno runtime)
- **Security**: Row Level Security (RLS) with PostgreSQL policies

#### API Layer & Communication
- **Primary API**: tRPC 10.x for type-safe client-server communication
- **Runtime**: Supabase Edge Functions with Deno runtime
- **AI Services**: Python FastAPI 0.104.x for AI processing
- **Type Safety**: End-to-end TypeScript with tRPC procedures
- **Inter-service**: HTTP/gRPC for Node.js ↔ Python communication

## Version Compatibility Matrix

| Component | Version | Compatibility | Notes |
|-----------|---------|---------------|-------|
| React | 19.0.0 | ✅ Next.js 14.x | Latest stable, new hooks |
| TypeScript | 5.6.0 | ✅ All components | Stable production version |
| Next.js | 14.x | ✅ React 19 | App Router, Edge Runtime |
| Fluent UI | 9.64.0 | ✅ React 19 | Latest v9 release |
| Office.js | ExcelApi 1.17+ | ✅ Modern Excel | Fallback to 1.4+ |
| Zustand | 4.4.7 | ✅ React 19 | State management |
| tRPC | 10.x | ✅ Next.js 14 | Type-safe APIs |
| Supabase | Latest | ✅ Edge Functions | Regional deployment |
| Python | 3.11+ | ✅ Agno Framework | AI/ML processing |
| Agno | Latest | ✅ Python 3.11+ | Multi-agent AI |

### Migration Paths

#### From Current to Recommended Versions
- **React 18 → 19**: Gradual migration using React 19 compiler
- **TypeScript 5.x → 5.6**: Update tsconfig.json, enable new features
- **Next.js 13 → 14**: App Router migration, Edge Runtime adoption
- **Fluent UI v8 → v9**: Component-by-component migration strategy

### AI Infrastructure
1. **Agent Framework (Agno)**
   - Multi-Agent Architecture
     - Advanced reasoning capabilities
     - Multi-modal support (text, image, audio, video)
     - Built-in memory and session storage
     - Vector database integration (20+ supported)
   - Model Integration
     - Model-agnostic (23+ providers)
     - Structured outputs with JSON mode
     - Native FastAPI integration
   - Performance Features
     - Ultra-fast instantiation (~3μs)
     - Low memory footprint (~5Kib per agent)
     - Built-in monitoring via agno.com
   - Development Features
     - Production-ready agent systems
     - Built-in RAG capabilities
     - Team-based agent collaboration
     - State management and persistence

2. **Specialized Agents**
Vision Agent:
- OCR processing for images and documents
- Table extraction from PDFs and images
- Receipt and invoice scanning
- Document layout analysis
- Image preprocessing and enhancement
- Batch image processing capabilities

File Processing Agent:
- Local file system integration
- Cloud storage integration (Google Drive, OneDrive)
- PDF parsing and text extraction
- CSV processing and transformation
- Batch file processing
- File format conversion and standardization

Data Analysis Agent:
Natural language processing for data analysis queries
Statistical analysis and hypothesis testing
Pattern recognition
Time series analysis and forecasting
Automated outlier detection
Cross-tabulation and pivot analysis
Data Cleaning Agent:
Automated data cleaning workflows
Missing value handling
Duplicate detection and removal
Format standardization
Data validation rules
Batch processing
Visualization Agent:
Smart chart and graph recommendations
Custom visualization generation
Interactive dashboard creation
African market-specific visualization templates
Export formatting for presentations
Formula Generation Agent:
Natural language to Excel formula conversion
Formula optimization
Complex formula explanation
Custom function generation
Error checking and validation
Python Code Agent:
Safe Python code generation
Code execution in sandbox environment
Statistical modeling
Advanced analytics functions
Integration with Excel data
Language Processing Agent:
Bilingual support (English/French)
Voice command processing
Context-aware conversations
Multi-turn analysis memory
Natural language query parsing
Data Integration Agent:
Excel file access and manipulation
OneDrive/Office 365 integration
External data source connections (SQL, Google Sheets, Oracle)
Web data extraction
Data enrichment through web research

3. **Agent Orchestration**
   - Task Distribution
   - Context Sharing
   - Error Handling
   - State Management
   - Agent Communication

### Analytics & Data Processing
1. **Analytics Engine**
   - Data Processing: pandas, numpy
   - Statistical Analysis: scipy, statsmodels
   - Machine Learning: scikit-learn
   - Visualization: matplotlib, seaborn
   - NLP: spaCy, NLTK, Transformers

2. **Sandbox Environment (Daytona)**
   - Isolation & Security
     - Container-based environments
     - Code static analysis
     - Input validation
     - Execution timeouts
     - Secure result channels
   - Resource Management
     - Memory limits
     - CPU restrictions
     - Disk space quotas
     - Network isolation
   - Development Features
     - Local testing support
     - Debug capabilities
     - Environment reproduction
     - Version control integration

3. **Data Analysis Features**
   - Statistical Analysis
   - Time Series Analysis
   - Forecasting
   - Pattern Recognition
   - Outlier Detection
   - Data Cleaning

### Integration & APIs
1. **Database Connectors**
   - SQL Databases
   - Oracle
   - Google Sheets
   - Web Data Extraction

2. **External Services**
   - PostHog (Analytics)
   - OpenReplay (Session Recording)
   - Sentry (Error Tracking)
   - Upstash (Rate Limiting)

### Security & Compliance
1. **Authentication & Authorization**
   - Supabase Auth
   - OAuth Integration
   - Row Level Security
   - RBAC Implementation

2. **Data Protection**
   - End-to-end Encryption
   - At-rest Encryption
   - Input Validation
   - Output Sanitization
   - Rate Limiting
   - Audit Logging

3. **Compliance**
   - SOC II Compliance
   - GDPR Compliance
   - African Data Protection Laws

### Backend Architecture Decision Analysis

#### Node.js vs Python Comparison for Excel + AI Integration

**Decision: Hybrid Architecture (Node.js Primary + Python AI Services)**

##### Node.js Advantages
- **Excel Integration**: Native Office.js compatibility and JavaScript ecosystem
- **Real-time Performance**: Superior WebSocket and real-time communication
- **Supabase Integration**: First-class tRPC and Edge Functions support
- **Development Velocity**: Single language across frontend and backend
- **Deployment Simplicity**: Seamless Vercel Edge Runtime deployment

##### Python Advantages
- **AI/ML Ecosystem**: Unmatched library support (pandas, scikit-learn, scipy)
- **Data Processing**: Superior performance for statistical analysis and modeling
- **Agno Framework**: Native Python support for multi-agent AI systems
- **Scientific Computing**: Mature ecosystem for advanced analytics

##### Chosen Architecture: Hybrid Approach
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Excel Add-in  │    │   Web App        │    │  AI Services    │
│   (React/TS)    │◄──►│  (Next.js/tRPC)  │◄──►│  (Python/Agno)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Supabase       │
                       │   (PostgreSQL)   │
                       └──────────────────┘
```

**Implementation Strategy:**
- **Primary Backend**: Node.js with tRPC for Excel integration and real-time features
- **AI Processing**: Python microservices with Agno framework for data analysis
- **Communication**: HTTP/gRPC between Node.js and Python services
- **Data Flow**: Excel ↔ Node.js ↔ Python ↔ Supabase

##### Technology Rationale

**Supabase Selection Rationale:**
- **Regional Availability**: Edge Functions deployable to African regions
- **Real-time Capabilities**: Built-in WebSocket support for live collaboration
- **Security**: Row Level Security (RLS) for multi-tenant data isolation
- **Developer Experience**: Excellent TypeScript support and tRPC integration
- **Cost Efficiency**: Competitive pricing for African market requirements
- **Compliance**: GDPR-compliant with African data residency options

**Alternative Considered: FastAPI + PostgreSQL**
- **Pros**: Pure Python stack, excellent AI integration
- **Cons**: Limited real-time capabilities, complex Excel integration, higher latency

### Performance & Infrastructure

#### Edge Computing Strategy
- **Regional Deployment**: Edge Functions optimized for African regions
- **CDN Integration**: Global content delivery with African edge locations
- **Connection Optimization**: Pooling and query optimization for regional latency
- **Bandwidth Efficiency**: Compressed responses and optimized asset delivery

#### Caching Architecture
- **Multi-Layer Caching**: Client-side, CDN, and database caching
- **Regional Cache**: Distributed caching for African market optimization
- **Static Assets**: Aggressive caching for UI components and documentation
- **Dynamic Content**: Smart caching for AI-generated insights and visualizations

#### African Market Infrastructure (Research Completed ✅)
- **Regional Optimization**:
  - **CDN**: Cloudflare (24 African POPs, Accra edge location for Ghana)
  - **Edge Functions**: Supabase deployment in South Africa region
  - **Payment Integration**: Flutterwave (primary) + Paystack (secondary) + MTN Mobile Money
  - **Compliance**: Ghana Data Protection Act and GDPR compliance
  - **Voice Processing**: OpenAI Whisper API for English/French multilingual support
- **Infrastructure Deployment**:
  - **Regional Database**: Supabase PostgreSQL with African region deployment
  - **CDN Strategy**: Cloudflare for West Africa, AWS/Azure for East/South Africa
  - **Connection Optimization**: Bandwidth-efficient operations, offline-first design
  - **Performance Monitoring**: PostHog + Sentry + custom African market metrics

### Research Completion Status ✅
- **AI Infrastructure**: Agno framework confirmed for multi-agent system
- **Voice Processing**: OpenAI Whisper API selected for multilingual accuracy
- **Payment Systems**: Flutterwave + Paystack integration strategy defined
- **Sandbox Security**: Pyodide/WASM primary + Docker fallback approach
- **Regional CDN**: Cloudflare confirmed for African optimization
- **Integration APIs**: Google Drive, OneDrive, database connectors researched

### Development & Quality
1. **Testing Infrastructure**
   - Unit Tests: Vitest
   - Component Tests: React Testing Library
   - E2E Tests: Playwright
   - Visual Testing: Storybook

2. **CI/CD**
   - Platform: GitHub Actions
   - Deployment: Vercel Pipeline
   - Quality Gates: Coverage, Performance, Security

3. **Code Quality**
   - TypeScript 5.x+ (Strict Mode)
   - ESLint
   - Prettier
   - Husky Pre-commit Hooks

4. **Documentation**
   - API: OpenAPI/Swagger
   - Code: TSDoc/JSDoc
   - Platform: Mintlify
   - Search: AI-powered docs search

### Monitoring & Analytics
1. **Performance Monitoring**
   - RUM (Real User Monitoring)
   - Core Web Vitals
   - Custom Metrics
   - Regional Performance

2. **Error Tracking**
   - Sentry Integration
   - Error Aggregation
   - Alert System
   - Error Analytics
